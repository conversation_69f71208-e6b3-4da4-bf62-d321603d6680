' 核心函数 - 修改曲线长度
Private Sub ModifyCurveLength(ByVal inputValue As Double, ByVal operation As String)
    On Error GoTo ErrorHandler
    
    Dim s As Shape
    Dim originalLength As Double
    Dim newLength As Double
    Dim scaleFactor As Double
    Dim processedCount As Integer
    Dim skippedCount As Integer
    
    ' 设置单位为毫米
    ActiveDocument.Unit = cdrMillimeter
    
    ' 开始命令组，便于撤销
    ActiveDocument.BeginCommandGroup "修改曲线长度"
    
    processedCount = 0
    skippedCount = 0
    
    ' 检查是否有选中的对象
    If ActiveSelectionRange.Count = 0 Then
        MsgBox "请先选择要修改的对象", vbExclamation
        Exit Sub
    End If

    ' 手动筛选曲线对象（更兼容的方式）
    Dim curveShapes As New ShapeRange
    Dim tempShape As Shape

    ' 遍历选中的对象，筛选出曲线对象
    For Each tempShape In ActiveSelectionRange
        If tempShape.Type = cdrCurveShape Then
            curveShapes.Add tempShape
        End If
    Next tempShape

    ' 检查是否找到曲线对象
    If curveShapes.Count = 0 Then
        MsgBox "没有找到要修改的曲线对象", vbExclamation
        Exit Sub
    End If

    ' 调试信息：显示找到的曲线数量
    ' MsgBox "找到 " & curveShapes.Count & " 个曲线对象", vbInformation

    ' 遍历找到的曲线对象
    For Each s In curveShapes
        On Error GoTo ShapeError

        ' 调试信息：显示当前处理的对象信息
        ' MsgBox "正在处理对象: " & s.Name & ", 类型: " & s.Type, vbInformation

        ' 获取原始长度
        originalLength = s.Curve.Length
        ' MsgBox "原始长度: " & originalLength & " mm", vbInformation

        ' 根据操作类型计算新长度
        Select Case operation
            Case "add"
                newLength = originalLength + inputValue
            Case "reduce"
                newLength = originalLength - inputValue
                If newLength <= 0 Then
                    skippedCount = skippedCount + 1
                    GoTo NextShape
                End If
            Case "set"
                newLength = inputValue
        End Select

        ' MsgBox "新长度: " & newLength & " mm", vbInformation

        ' 计算缩放因子
        scaleFactor = newLength / originalLength
        ' MsgBox "缩放因子: " & scaleFactor, vbInformation

        ' 应用缩放
        Dim newWidth As Double
        newWidth = s.SizeWidth * scaleFactor
        ' MsgBox "新宽度: " & newWidth & " mm", vbInformation

        ' 修改曲线长度
        s.SetSizeEx s.CenterX, s.CenterY, newWidth

        processedCount = processedCount + 1
        On Error GoTo ErrorHandler
NextShape:
    Next s
    
    ' 结束命令组
    ActiveDocument.EndCommandGroup

    ' 刷新视图
    ActiveWindow.Refresh

    ' 如果是减少操作且有跳过的曲线对象，显示提示
    If operation = "reduce" And skippedCount > 0 Then
        MsgBox "有 " & skippedCount & " 个曲线对象的长度小于您给出的数值，修改失败", vbExclamation
    End If
    

    Exit Sub
    
    Exit Sub



ShapeError:
    ' 处理单个对象时的错误
    MsgBox "处理对象时发生错误: " & Err.Description & vbCrLf & "错误号: " & Err.Number & vbCrLf & "对象名称: " & s.Name, vbCritical
    Resume NextShape

ErrorHandler:
    ' 如果出错，结束命令组
    On Error Resume Next
    ActiveDocument.EndCommandGroup
    MsgBox "修改曲线长度时发生错误: " & Err.Description & vbCrLf & "错误号: " & Err.Number, vbCritical
End Sub

Private Sub TextBox1_Change()
    ' 在CorelDRAW VBA中，限制输入只能是数字和小数点
    Dim i As Integer
    Dim newText As String
    Dim hasDecimal As Boolean

    newText = ""
    hasDecimal = False

    ' 逐字符检查，只保留数字和一个小数点
    For i = 1 To Len(Me.TextBox1.Text)
        Dim char As String
        char = Mid(Me.TextBox1.Text, i, 1)

        If char >= "0" And char <= "9" Then
            ' 数字字符，保留
            newText = newText + char
        ElseIf char = "." And Not hasDecimal Then
            ' 小数点，且之前没有小数点，保留
            newText = newText + char
            hasDecimal = True
        End If
        ' 其他字符忽略
    Next i

    ' 如果处理后的文本与原文本不同，更新TextBox
    If newText <> Me.TextBox1.Text Then
        Me.TextBox1.Text = newText
    End If
End Sub

Private Sub CommandButton1_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim addLength As Double
    addLength = CDbl(Me.TextBox1.Text)

    If addLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength addLength, "add"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

Private Sub CommandButton2_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim reduceLength As Double
    reduceLength = CDbl(Me.TextBox1.Text)

    If reduceLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength reduceLength, "reduce"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

Private Sub CommandButton3_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim targetLength As Double
    targetLength = CDbl(Me.TextBox1.Text)

    If targetLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength targetLength, "set"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub