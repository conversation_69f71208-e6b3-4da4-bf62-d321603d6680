Option Explicit

' 曲线长度修改工具 - 主函数
Sub 曲线长度修改工具()
    On Error GoTo ErrorHandler

    ' 检查是否有文档打开
    If Documents.Count = 0 Then
        MsgBox "请先打开文档", vbExclamation
        Exit Sub
    End If

    ' 检查是否有对象被选中
    If ActiveSelectionRange.Count = 0 Then
        MsgBox "请先选择要修改的曲线对象", vbExclamation
        Exit Sub
    End If

    ' 显示窗口
    CurveForm.Show
    Exit Sub

ErrorHandler:
    MsgBox "发生错误: " & Err.Description, vbCritical
End Sub

' 增加曲线长度
Sub 增加曲线长度()
    On Error GoTo ErrorHandler

    ' 获取输入值
    Dim inputValue As String
    inputValue = InputBox("请输入要增加的长度（毫米）:", "增加曲线长度", "10")

    If inputValue = "" Then Exit Sub
    If Not IsNumeric(inputValue) Then
        MsgBox "请输入有效的数字", vbExclamation
        Exit Sub
    End If

    Dim addLength As Double
    addLength = CDbl(inputValue)

    If addLength <= 0 Then
        MsgBox "请输入大于0的数值", vbExclamation
        Exit Sub
    End If

    ' 对选择的所有对象依次增加长度
    ModifyCurveLength addLength, "add"
    Exit Sub

ErrorHandler:
    MsgBox "增加曲线长度时发生错误: " & Err.Description, vbCritical
End Sub

' 减少曲线长度
Sub 减少曲线长度()
    On Error GoTo ErrorHandler

    ' 获取输入值
    Dim inputValue As String
    inputValue = InputBox("请输入要减少的长度（毫米）:", "减少曲线长度", "10")

    If inputValue = "" Then Exit Sub
    If Not IsNumeric(inputValue) Then
        MsgBox "请输入有效的数字", vbExclamation
        Exit Sub
    End If

    Dim reduceLength As Double
    reduceLength = CDbl(inputValue)

    If reduceLength <= 0 Then
        MsgBox "请输入大于0的数值", vbExclamation
        Exit Sub
    End If

    ' 对选择的所有对象依次减少长度
    ModifyCurveLength reduceLength, "reduce"
    Exit Sub

ErrorHandler:
    MsgBox "减少曲线长度时发生错误: " & Err.Description, vbCritical
End Sub

' 设置曲线长度
Sub 设置曲线长度()
    On Error GoTo ErrorHandler

    ' 获取输入值
    Dim inputValue As String
    inputValue = InputBox("请输入目标长度（毫米）:", "设置曲线长度", "100")

    If inputValue = "" Then Exit Sub
    If Not IsNumeric(inputValue) Then
        MsgBox "请输入有效的数字", vbExclamation
        Exit Sub
    End If

    Dim targetLength As Double
    targetLength = CDbl(inputValue)

    If targetLength <= 0 Then
        MsgBox "请输入大于0的数值", vbExclamation
        Exit Sub
    End If

    ' 对选择的所有对象依次修改为指定长度
    ModifyCurveLength targetLength, "set"
    Exit Sub

ErrorHandler:
    MsgBox "设置曲线长度时发生错误: " & Err.Description, vbCritical
End Sub

' 核心函数 - 修改曲线长度
Private Sub ModifyCurveLength(ByVal inputValue As Double, ByVal operation As String)
    On Error GoTo ErrorHandler
    
    Dim s As Shape
    Dim originalLength As Double
    Dim newLength As Double
    Dim scaleFactor As Double
    Dim processedCount As Integer
    Dim skippedCount As Integer
    
    ' 设置单位为毫米
    ActiveDocument.Unit = cdrMillimeter
    
    ' 开始命令组，便于撤销
    ActiveDocument.BeginCommandGroup "修改曲线长度"
    
    processedCount = 0
    skippedCount = 0
    
    ' 遍历所有选中的对象
    For Each s In ActiveSelectionRange
        ' 检查是否为曲线对象
        If s.Type = cdrCurveShape Then
            ' 获取原始长度
            originalLength = s.Curve.Length
            
            ' 根据操作类型计算新长度
            Select Case operation
                Case "add"
                    newLength = originalLength + inputValue
                Case "reduce"
                    newLength = originalLength - inputValue
                    ' 确保新长度不为负数或零
                    If newLength <= 0 Then
                        MsgBox "警告：曲线 " & (processedCount + skippedCount + 1) & " 的长度减少后将小于等于0，跳过此对象", vbExclamation
                        skippedCount = skippedCount + 1
                        GoTo NextShape
                    End If
                Case "set"
                    newLength = inputValue
            End Select
            
            ' 计算缩放因子
            scaleFactor = newLength / originalLength
            
            ' 应用缩放
            Dim newWidth As Double
            newWidth = s.SizeWidth * scaleFactor
            
            ' 修改曲线长度
            s.SetSizeEx s.CenterX, s.CenterY, newWidth
            
            processedCount = processedCount + 1
        Else
            skippedCount = skippedCount + 1
        End If
NextShape:
    Next s
    
    ' 结束命令组
    ActiveDocument.EndCommandGroup
    
    ' 刷新视图
    ActiveWindow.Refresh
    
    ' 显示处理结果
    Dim resultMsg As String
    resultMsg = "处理完成！" & vbCrLf & _
                "成功处理: " & processedCount & " 个曲线对象" & vbCrLf & _
                "跳过: " & skippedCount & " 个非曲线对象"
    
    MsgBox resultMsg, vbInformation
    Exit Sub
    
ErrorHandler:
    ' 如果出错，结束命令组
    On Error Resume Next
    ActiveDocument.EndCommandGroup
    MsgBox "修改曲线长度时发生错误: " & Err.Description, vbCritical
End Sub