Option Explicit

' TextBox1数值验证 - 只允许输入数字和小数点
Private Sub TextBox1_KeyPress(ByVal KeyAscii As Integer)
    If KeyAscii = 8 Then Exit Sub ' 退格键
    If KeyAscii >= 48 And KeyAscii <= 57 Then Exit Sub ' 数字0-9
    If KeyAscii = 46 Then ' 小数点
        If InStr(Me.TextBox1.Text, ".") > 0 Then
            KeyAscii = 0
        End If
        Exit Sub
    End If
    KeyAscii = 0
End Sub

' CommandButton1点击事件 - 增加曲线长度
Private Sub CommandButton1_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim addLength As Double
    addLength = CDbl(Me.TextBox1.Text)

    If addLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength addLength, "add"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

' CommandButton2点击事件 - 减少曲线长度
Private Sub CommandButton2_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim reduceLength As Double
    reduceLength = CDbl(Me.TextBox1.Text)

    If reduceLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength reduceLength, "reduce"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

' CommandButton3点击事件 - 修改曲线长度为指定数值
Private Sub CommandButton3_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim targetLength As Double
    targetLength = CDbl(Me.TextBox1.Text)

    If targetLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength targetLength, "set"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

    If targetLength <= 0 Then
        MsgBox "请输入大于0的数值", vbExclamation
        Exit Sub
    End If

    ' 对选择的所有对象依次修改为指定长度
    ModifyCurveLength targetLength, "set"
    Exit Sub

ErrorHandler:
    MsgBox "设置曲线长度时发生错误: " & Err.Description, vbCritical
End Sub

' 核心函数 - 修改曲线长度
Private Sub ModifyCurveLength(ByVal inputValue As Double, ByVal operation As String)
    On Error GoTo ErrorHandler
    
    Dim s As Shape
    Dim originalLength As Double
    Dim newLength As Double
    Dim scaleFactor As Double
    Dim processedCount As Integer
    Dim skippedCount As Integer
    
    ' 设置单位为毫米
    ActiveDocument.Unit = cdrMillimeter
    
    ' 开始命令组，便于撤销
    ActiveDocument.BeginCommandGroup "修改曲线长度"
    
    processedCount = 0
    skippedCount = 0
    
    ' 遍历所有选中的对象
    For Each s In ActiveSelectionRange
        ' 检查是否为曲线对象
        If s.Type = cdrCurveShape Then
            ' 获取原始长度
            originalLength = s.Curve.Length
            
            ' 根据操作类型计算新长度
            Select Case operation
                Case "add"
                    newLength = originalLength + inputValue
                Case "reduce"
                    newLength = originalLength - inputValue
                    ' 确保新长度不为负数或零
                    If newLength <= 0 Then
                        MsgBox "警告：曲线 " & (processedCount + skippedCount + 1) & " 的长度减少后将小于等于0，跳过此对象", vbExclamation
                        skippedCount = skippedCount + 1
                        GoTo NextShape
                    End If
                Case "set"
                    newLength = inputValue
            End Select
            
            ' 计算缩放因子
            scaleFactor = newLength / originalLength
            
            ' 应用缩放
            Dim newWidth As Double
            newWidth = s.SizeWidth * scaleFactor
            
            ' 修改曲线长度
            s.SetSizeEx s.CenterX, s.CenterY, newWidth
            
            processedCount = processedCount + 1
        Else
            skippedCount = skippedCount + 1
        End If
NextShape:
    Next s
    
    ' 结束命令组
    ActiveDocument.EndCommandGroup
    
    ' 刷新视图
    ActiveWindow.Refresh
    
    ' 显示处理结果
    Dim resultMsg As String
    resultMsg = "处理完成！" & vbCrLf & _
                "成功处理: " & processedCount & " 个曲线对象" & vbCrLf & _
                "跳过: " & skippedCount & " 个非曲线对象"
    
    MsgBox resultMsg, vbInformation
    Exit Sub
    
ErrorHandler:
    ' 如果出错，结束命令组
    On Error Resume Next
    ActiveDocument.EndCommandGroup
    MsgBox "修改曲线长度时发生错误: " & Err.Description, vbCritical
End Sub