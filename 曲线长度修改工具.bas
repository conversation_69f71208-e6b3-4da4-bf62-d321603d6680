' 核心函数 - 修改曲线长度
Private Sub ModifyCurveLength(ByVal inputValue As Double, ByVal operation As String)
    On Error GoTo ErrorHandler
    
    Dim s As Shape
    Dim originalLength As Double
    Dim newLength As Double
    Dim scaleFactor As Double
    Dim processedCount As Integer
    Dim skippedCount As Integer
    
    ' 设置单位为毫米
    ActiveDocument.Unit = cdrMillimeter
    
    ' 开始命令组，便于撤销
    ActiveDocument.BeginCommandGroup "修改曲线长度"
    
    processedCount = 0
    skippedCount = 0
    
    ' 遍历所有选中的对象
    For Each s In ActiveSelectionRange
        ' 检查是否为曲线对象
        If s.Type = cdrCurveShape Then
            ' 获取原始长度
            originalLength = s.Curve.Length
            
            ' 根据操作类型计算新长度
            Select Case operation
                Case "add"
                    newLength = originalLength + inputValue
                Case "reduce"
                    newLength = originalLength - inputValue
                    If newLength <= 0 Then
                        skippedCount = skippedCount + 1
                        GoTo NextShape
                    End If
                Case "set"
                    newLength = inputValue
            End Select
            
            ' 计算缩放因子
            scaleFactor = newLength / originalLength
            
            ' 应用缩放
            Dim newWidth As Double
            newWidth = s.SizeWidth * scaleFactor
            
            ' 修改曲线长度
            s.SetSizeEx s.CenterX, s.CenterY, newWidth
            
            processedCount = processedCount + 1
        Else
            skippedCount = skippedCount + 1
        End If
NextShape:
    Next s
    
    ' 结束命令组
    ActiveDocument.EndCommandGroup
    
    ' 刷新视图
    ActiveWindow.Refresh
    

    Exit Sub
    
ErrorHandler:
    ' 如果出错，结束命令组
    On Error Resume Next
    ActiveDocument.EndCommandGroup
    MsgBox "修改曲线长度时发生错误: " & Err.Description, vbCritical
End Sub

Private Sub TextBox1_Change()
    ' 在CorelDRAW VBA中，TextBox使用Change事件而不是KeyPress事件
    ' 这里可以添加数值验证逻辑，但通常在使用时进行验证即可
End Sub

Private Sub CommandButton1_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim addLength As Double
    addLength = CDbl(Me.TextBox1.Text)

    If addLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength addLength, "add"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

Private Sub CommandButton2_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim reduceLength As Double
    reduceLength = CDbl(Me.TextBox1.Text)

    If reduceLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength reduceLength, "reduce"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub

Private Sub CommandButton3_Click()
    On Error GoTo ErrorHandler

    If Me.TextBox1.Text = "" Or Not IsNumeric(Me.TextBox1.Text) Then
        Exit Sub
    End If

    Dim targetLength As Double
    targetLength = CDbl(Me.TextBox1.Text)

    If targetLength <= 0 Then
        Exit Sub
    End If

    ModifyCurveLength targetLength, "set"
    Exit Sub

ErrorHandler:
    MsgBox "错误: " & Err.Description, vbCritical
End Sub