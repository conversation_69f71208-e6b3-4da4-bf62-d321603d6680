在CorelDRAW和Corel Designer文档中使用 Corel查询语言( CQL)搜索对象
在CorelDRAW和Corel designer编写宏时，搜索具有特定属性的对象是一项常见任务。通常，如果需要搜索特定大小或填充的对象，则需要循环遍历页面上的所有 Shape，检查每个 Shape的属性并确定其是否符合标准。例如，利用宏删除所有没有填充和没有轮廓的对象： 
Sub DeleteInvisible()    Dim s As Shape    Dim sr As New ShapeRange    For Each s In ActivePage.Shapes.FindShapes()
        If s.Outline.Type = cdrNoOutline And s.Fill.Type = cdrNoFill Then            sr.Add s
        End If    Next s    sr.Delete 
End Sub
对于更复杂的条件，需要更多的检查。值得注意的是：我们在页面上找到的每个 Shape都要进行这些检查。您还可以在 Shapes.FindShapes 方法中使用特殊查询表达式，该方法允许您筛选由该方法返回的Shape。所提供的表达式使用 CQL指定一个 Shape必须满足的条件集，以便包含在搜索结果中。因此，为了完成相同的任务，删除没有填充和没有轮廓的对象，使用这种新的方法，我们可以重写上面的宏，像这样： 
Sub DeleteInvisible()    Dim sr As ShapeRange    Set sr = ActivePage.Shapes.FindShapes( _
              Query:="@outline.type = 'none' and @fill.type = 'none'")    sr.Delete End Sub
可以在 FindShapes的查询参数中使用的条件比这要复杂得多。例如，下面的宏在填充或轮廓中选择任何具有红色的对象： 
Sub SelectShapesWithRedColor()    ActivePage.Shapes.FindShapes(Query:="@colors.find('red')").CreateSelection End Sub 

CQL是一种面向对象的查询语言。这意味着它运行所需的所有数据都是某种类型的对象，并且这些对象具有某些方法，这些方法可以被调用来访问这些对象的属性。有许多预定义的数据对象，它们代表数字、字符串等以及特定任务 CQL的自定义对象。 CQL不仅可以用于查找文档中的 Shape，还可以用于其他对象（例如，它可以用来查找具有字体、大小和颜色等特定属性的文本字符）。 CQL的常用内置数据对象包括以下内容：这些对象存在于 CQL的任何指令中： NULL：表示不存在属性的空对象。 bool：布尔值（ true或false）。 INT:整数值（介于-2147483648…2147483647之间）。 
-308 308 
double：浮点值（大约在 10 ... 10之间） string：Unicode文本字符串。 unit：计量单位。目前可以使用线性计量单位，如英寸（ in）、毫米（ mm）和其他单位。 COM：打包的 COM对象。通过它可以实现调用 VBA对象模型方法，如 Shape、填充、轮廓、颜色等。 CorelDRAW和Corel designer对象模型提供的可用的属性是有限的，而通过 COM对象则可调用额外的对象信息。 array：任何 CQL类型的对象列表。 custom：由 CQL指令提供的其他对象类型，即在 Corel文档中查找 Shape的CQL自定义对象，如形状（ shape）、填充（ fill）、轮廓（ outline）和颜色（ color）。

每个 CQL表达式都可以计算出单个值（对象）。在文档中查找 Shape时，对文档中的每个 shape进行查询表达式求值，并将结果值转换为布尔值（ true或false），以确定给定的 shape是否与标准匹配。如果结果为“ true”，则将 shape添加到已找到 shape的列表中，否则将忽略该 shape。
但是，一般来说， CQL表达式可以对任何对象类型进行运算，例如整数、字符串、对象数组等。这里有几个有效的 CQL表达式示例： 1 + 2 2 * (2.3 + 1.2) 'Hello' + 'world’ CorelDRAW和Corel desinger的VBA对象模型有很多帮助您计算 CQL表达式和故障诊断查询的方法。 Application.Evaluate是其中之一；它计算 CQL表达式并返回计算结果。 
Sub TestEvaluate()    MsgBox Evaluate("2*(2.3+1.2)") End Sub
上面的宏只会显示一个包含“ 7”的消息框，这是算术表达式的结果。该方法有助于快速检查 CQL表达式的有效性或确认结果值。表达式中的每个操作数(如 2、2.3或1.2)都是一个对象，即便是常数，如 1或“ hello”。对于常量，数据类型是自动确定的。每个对象都可以有自己的方法和属性集。要调用对象的方法，使用以下语法： 
object.method(parameters)
不需要任何参数的方法可以在不使用圆括号的情况下调用。以下两种表达式都是有效的： 'world'.length()    或者  'world'.length以上两个表达式都计算为 5的数值，即字符串“ world”的长度。与大多数编程语言一样，运算符具有一定的优先级。优先运算具有较高优先级的运算符。下面是运算符按优先级顺序排列的顺序（按优先级递减顺序）：

运算符 数据类型 作用 示例 结果  
+  numeric 加  1 + 2  3  
+  string 文本连接  'Hello'+'world'  'Hello world'  
- numeric 减  10月4日  6  
*  numeric 乘  2 * 5  10  
/  numeric 除  3月2日  1.5  
\  numeric 整除  3 \ 2  1  
^  numeric 次方  2 ^ 3  8  
>  any 大于  3 > 2  TRUE  
<  any 小于  10 &lt 3  FALSE  
>=  any 大于等于  2 >= 2  TRUE  
<=  any 小于等于  3 <= 10  TRUE  
=, ==  any 等于  2 = 3  FALSE  
<>  any 不等于  2 <> 3  TRUE  
=  string 不精确相等  'Test' = 'test'  TRUE  
==  string 精确相等  'Test' =='test'  FALSE  
<>  string 不精确不相等  'Test' <>'test'  FALSE  
&gt, >=, =, <=,  string 精确不相等  'Test' >='test'  FALSE  
&, and  bool 逻辑且  2 = 2 and 1 = 1  TRUE  
|, or  bool 逻辑或  2 = 1 or 1 = 1  TRUE  
!, not  bool 逻辑非  not (2 = 2)  FALSE  
.  object 对象方法调用符  (-1).abs()  1  



    . (对象方法调用 ) ^ 


*, /, \ 

+, -

>, <, =, ==, <=, >=, <> 

not, ! 

and, or, &, |因此，在下面的表达式中： 2 * 2 + 3，首先进行乘法运算，然后加法运算，因为乘法具有较高的优先权。为了修改优先级，可以使用括号（）： 2 * (2 + 3) 

CQL通常与对象一起工作。所有函数都是对象的方法。但有一个特殊的对象，它具有特殊的意义——全局对象( global)。该对象的方法直接调用而不指定对象名称。该对象提供了一组公共函数和常数，如 Pi、e、Sin、Cos、Min、Max等。 "sin(2)" = 0.909297426825682 "max(1, 2, 4, 3, 0)" = 4 "pi" = 3.14159265358979在全局对象 global参考下可以找到更详细的说明。

CQL可以用于任何类型的数据上执行的查询。在 CorelDRAW和CoreLDesigner文档中查找 Shape仅仅是一种应用，而查找到文本属性则是另一种。在未来，可以实现 CQL的新应用（例如搜索特定属性的文件，或电子邮件联系人）。在 CQL的每个应用中，提供了允许您访问对象特定数据的不同自定义对象。例如，当搜索 Shape时， “Shape”对象被提供给 CQL，允许您检查 Shape的宽度或高度、填充和轮廓属性、名称和许多其他属性。当在文本范围内搜索文本时，“文本”对象会提供诸如字体、大小、颜色、样式、大小以及更多的属性。
如需在 cql查询中访问当前对象的方法和属性，请用“ @”字符放置于其方法名称前。因此，当搜索Shape时，“@ width”将调用当前“ shape”对象的“ width”方法并返回其宽度。当前查询对象的方法可以返回任何内置类型或任何其他自定义对象类型的值。例如，如果表达式是用来计算 Shape的，并且引用了它的“ fill”属性，则返回的对象是另一种自定义对象，它提供 Shape的填充属性，因此您可以在查询中使用它，如下： "@fill.color.name = 'Red'" 

CQL支持一种特殊的“ help”方法，该方法列出所有对象的所有可用方法和属性。例如，为了获得“ int”对象上可用的方法的列表，可以对以下表达式进行评估：“ Int.help”。在VBA编辑器的即时窗口中使用 Application.Evaluate 方法是最容易的，以获得对象方法的帮助。转到 VBA编辑器中的即时窗口（ CTRL-G）和输入： 
? Evaluate("int.help")然后点击回车键。结果将呈现在窗口中： 
int.Abs() int.ToRadians() int.ToDegrees() int.Radix(int, [int]) int.ToBool() int.ToInt() int.ToDouble() int.ToString() int.TypeName() {Static} int.This() int.IsNumeric() int.IsNull() 

在许多情况下，需要在表达式中检查同一对象的各种属性。为了减少表达式重复，可以使用一个特殊的表达式来重复引用同一个类的方法。 CQL规定特殊构造方括号[]可用于此。它的一般语法如下： "object[.method1 + .method2 * .method3]”例如，如果需要检查 Shape填充的几个属性，可以使用以下表达式： "@fill[.type = 'uniform' and .color = 'red']”上面的表达式相当于下面的表达式： "@fill.type = 'uniform' and @fill.color = 'red’下面是另一个例子： "'Test'[.toUpper + .toLower]" => returns "TESTtest" 

CQL内置支持对计量单位的计算。线性尺寸如 mm、in、m、cm、Pt等将以特殊的方式处理。为了提供一个恒定的测量单位，单位值连同测量单位必须全部包含在花括号{}中。例如： {1 in} = 1 inch {1 mm} = 1 millimeter
某些数学运算可以在测量单位上进行： "{1 cm} + {1 mm}" => {1.1 cm} "{1 pt} * 3" => {3 pt} 

CQL还支持测量单位的乘法和除法。因此，单位的平方、立方等也得到支持。可以使用以下一般语法指定不同次方的单位：{值单位 ^幂}例如： {2 mm^2} => 2 sq. mm {3 in^3} => 3 cubic inches
幂为零的单位（{ 2 mm ^ 0 }）等于常规数值，即“{ 2 mm ^ 0 }”=2的数值。 2和3的幂可以通过使用特殊字符 "2" ( U+00B2) 和 "3" (U+00B3) 在测量单位之后指定： {2 mm2} => 2 sq. mm {3 in3} => 3 cubic inches
单位相乘增加单位的幂，单位相除会降低它们的幂： "{2 mm} * {2 mm}" => {4 mm2} "{4 mm2} / {1 mm}" => {4 mm}
比较单位只能对相同幂值的单位来进行： "{2 mm} * {2 mm} = {4 mm2}" => true (正确 ) "{2 mm} * {2 mm} = {4 mm}" => 错误 : comparing mm2 to mm
然而，相同的单位类别的不同单位可以用于同一表达式： "{1 in} = {72 pt}" => true
进行计量单位的比较会直接导致精度的降低，以此来消除舍入误差。在 CQL中，线性计量单位的精度通常被设置为比较中所涉及的最大单位的小数点后 3位。为了精确比较，可以使用精确相等运算符（“＝＝”）。考虑到 1 mm＝2.83464566929134 Pt，以下表达式将产生： 
"{1 mm} = {2.835 pt}" => true "{1 mm} == {2.835 pt}" => false
转换单位是相对于幂值相同单位进行的。要转换不同单位之间的值，必须使用 'unit.convert'方法： "{1 in}.convert('mm')" => {25.4 mm} "{1 in2}.convert('mm')" => {645.16 mm2} 

方法 示例 结果  
unit.Category()  {1 in3 }.category  'Linear Dimensions'  
unit.Power()  {1 in3 }.power  3  
unit.Unit()  {1 in3 }.unit  'in'  
unit.DisplayUnit()  {1 in3 }.displayUnit  'in3 '  
unit.Value()  {1 in3 }.value  1  
unit.BaseValue()  {1 in}.baseValue  254000  
unit.Convert(string)  {1 in3 }.convert('mm')  316387.064 mm  


CQL对象的基本类
大多数 CQL内置类都是从一个基本类派生而来，它提供了一些常见的功能和方法。下面描述的所有对象还包括基本类的方法（只有不从这个基本类派生的‘ global’对象除外）。 
object.ToBool()
将当前对象转换为布尔值（ TRUE或FALSE）。 
object.ToInt()
将对象的值转换为整数。 
object.ToDouble()
将对象的值转换为双精度。 
object.ToString()
将对象的值转换为文本。 
object.TypeName()
将对象类型名称作为字符串获取。 
object.This()
返回对象的副本。 
object.IsNumeric()
如果对象可以转换为数值（ int或double），则返回 true。 
object.IsNull()
如果对象是空对象，则返回 true 

NULL对象没有任何其他方法，用于表示缺少的属性或对象。在空对象上调用任何方法是合法的，调用将成功，返回值将为空。因此， null.somemethod() 将返回“ NULL”。比较运算符=，=，<，>，> =，<=涉及空对象总是返回 false。因此， “NULL＝5”将是错误的。唯一的例外是比较两个空对象与=，= =。在这种情况下，相等运算符返回 true。当两个操作数都为空时，对于不等式运算符< >，空对象与非空对象进行比较时总是返回 true，双方都为空对象则返回 false。布尔对象没有从基本类继承的那些方法。布尔值可以有“ true”或“ false”的值，并且是比较运算符的默认返回值（<，>，<，=等）。
示例 结果  
null = 3  FALSE  
null = 'test'  FALSE  
null = null  TRUE  
null <> 3  TRUE  
null <> null  FALSE  
3 > null  FALSE  
null > null  FALSE  
null >= null  FALSE 



int.Abs()
返回整数的绝对值 
(-2).abs => 2 
int.ToRadians()
将度数值转换为弧度： 
180.ToRadians => 3.14159265358979 
int.ToDegrees()
将弧度转换为度数值： 
1.ToDegrees => 57.2957795130823 
int.Radix(int, [int])
将数字转换为不同进制数。此方法的结果是字符串。第一个参数指定数字的新进制，第二个参数
可选，指定返回的字符串长度。如果实际字符串较小，则用零字符（“ 0”）代替填充： 28.radix(16) => '1c' 28.radix(16,4) => '001c' 28.radix(2) => '11100' 

双精度对象表示浮点值。它具有与上面 int对象相同的附加方法： 
double.Abs()
返回双精度值的绝对值： 
(-2.5).abs => 2.5 
double.ToRadians()
将度数值转换为弧度： 
(12.5).ToRadians => 0.218166156499291 
double.ToDegrees()
将弧度转换为度数值： 
pi.ToDegrees => 180.0 
int.Radix(int, [int])
将数字转换为不同进制。在进制改变之前，浮点值首先被转换成整数。

String对象表示 Unicode字符串值，该值可以为空或包含多个字符 
string.Length()
返回文本长度 'test'.length => 4 
string.Empty()
如果字符串为空，则返回 true： 'test'.empty => false 
string.ToUpper()
将字符串转换为大写： 'test'.ToUpper => 'TEST’ 
string.ToLower()
将字符串转换为小写： 'TEST'.ToLower => 'test’ 
string.ToTitle()
将字符串转换为标题： 'TEST'.ToTitle => 'Test’ 
string.CharCode()
获取第一个字符串字符的 Unicode字符代码： 'Test'.CharCode => 84 
string.SubStr(int, [int])
获取字符串的子字符串。第一个参数是包含在子串中的第一个字符的零基索引。第二个可选参数是要检索的子字符串的长度。如果省略，则返回字符串的其余部分： 'Hello World'.SubStr(6) => 'World' 'Hello World'.SubStr(6,3) => 'Wor’第一个索引可以是负值，它表示从字符串的结尾开始索引： 'This is a test'.SubStr(-4) => 'test’如果最后一个参数为负数，则表示字符串结尾的最后一个字符： 'Some words'.SubStr(5, -1) => 'word’ 
string.Delete(int, [int])
从字符串中移除指定的字符。参数的含义与上面描述的子函数的含义相同： 'abcdefg'.Delete(2, 3) => 'abfg' 'abcdefg'.Delete(-2) => 'abcdeg’ 
string.Trim([string])
从字符串的开头和结尾移除指定字符。如果省略了可选字符串参数，则删除空格（ code 32）和 tab（code 9）字符： ' test  ‘.trim() => 'test' 
'aaaaatestbbbccc'.trim('abc') => 'test’ 
string.LTrim([string])
类似于 Trim函数，但仅从字符串的开头移除字符： ' test  ‘.trim() => 'test  ‘ 
string.RTrim([string])
类似于 Trim函数，但仅从字符串的结尾移除字符： ' test  ‘.trim() => '  test’ 
string.Repeat(int)
按指定次数重复字符串： 'test'.repeat(3) => 'testtesttest’ 
string.StartsWith(string)
如果字符串从指定字符串开始，则返回 true： 'Apple'.StartsWith('A') => true 
string.EndsWith(string)
如果字符串从指定字符串结束，则返回 true： 'Cooking'.EndsWith('ing') => true 
string.Contains(string)
如果字符串包含指定字符串，则返回 true： 'Peaceful world'.Contains('Peace') => true 
string.Replace(string, string)
用替换字符串（第二个参数）替换搜索子串（第一个参数）的所有实例： 'green pen, blue pen'.Replace('pen', 'pencil') => 'green pencil, blue pencil’ 
string.Split(string)
将指定字符分隔的字符串拆分为字符串数组： ' Design< ->Implementation< ->Testing '.Split( '<->') => array( 'Design' , 'Implementation', 'Testing') 
string.CharAt(int)
获取字符串中给定索引的字符。该索引起始为零： 'Test'.CharAt(1) => 'e’ 
string.CharCodeAt(int)
获取给定索引中字符的 Unicode字符代码。该索引起始为零： 'ABCD'.CharCodeAt(3) => 68 
string.IndexOf(string)
查找子字符串并返回其第一次出现的字符索引。如果未找到，则返回-1。 'this is a test'.IndexOf('is') => 2 
string.LastIndexOf(string)
查找子字符串并返回其最后一个出现的字符索引。如果未找到，则返回-1。 'this is a test'.LastIndexOf('is') => 5 
string.ToCharArray()
将字符串转换为字符数组： 'abc'.ToCharArray => array('a', 'b', 'c') 
string.ParseInt([int])
将字符串转换为整数值。可选参数指定要从中转换的进制。此方法总能成功解析字符串，直到遇到它不能基于给定进制识别出有效数字的第一个字符。 '1c'.ParseInt(16) => 28。数组是一组任何类型对象的列表。 

array.Count()
返回数组中元素的数量： 
array(1,2,3).count => 3 
array.Empty()
如果数组不包含元素，则返回 true： array(1,2,3).empty => false array().empty => true 
array.Add(object)
将对象添加到数组的末尾 
array.Item(int)
获取由索引标识的对象。索引是基于 1的： array(1,3,4).item(2) => 3 
array.Join(string)
使用指定的分隔符字符串将数组元素连接到字符串： 
array(1,3,4).join(', ') => '1, 3, 4' 
array.ForEach(object, expression, [object])
计算数组中每个元素的指定表达式，并返回计算结果。这是一个迭代过程。先前迭代的计算结果被传递到当前的迭代结果。正在计算的表达式可以使用具有这些成员变量的特殊迭代对象： $item -当前数组元素 $index -当前数组元素的基于 1的索引 $lasteval -前一次迭代的计算结果 $data -调用方传递的附加数据对象。第一个参数指定通过$ lasteval变量传递给第一个元素迭代的初始值。第二个参数是为每个数组元素求值的表达式，最后一个可选参数是作为$ data变量传递的附加数据对象。如果省略此参数， $data变量将包含 null对象。 array(1,2,3,4).foreach(0, $lasteval + $item) => 10 (数组所有元素相加 ) array('this', 'is','an', 'example').foreach('Result:', $lasteval + ' ' + $item.ToTitle) =>结果 : This Is An Example 
array.Filter(expression, [object])
通过只包含布尔表达式指定为真的元素来创建数组元素的子集。表达式可以使用与 ForEach 方法中相同的迭代对象来获取当前元素的值和作为第二个参数传递的自定义数据。 
array(1,2,3,4,5).filter($item > 3) => array(4, 5) 
array.Convert(expression, [object])
将数组中的每个元素的值更改为给定表达式的求值结果。 
array(1,2,3,4).convert($item * 2) => array(2, 4, 6, 8) 
array.Find(object)
在数组中查找对象并在找到时返回 true array(1,2,3,4).find(3) => true 
array.IndexOf(object)
在数组中查找对象并返回数组元素的基于 1的索引，如果未找到则返回 0。 array('apple', 'orange', 'pear').indexof('orange') => 2 
array.First
返回数组的第一个元素 
array.Last
返回数组的最后一个元素单位对象表示一个附加了特定度量单位的值。单元对象具有以下方法（参见上面单元的详细信息）： 

unit.Category()
返回字符串的单位类别的描述 
unit.Power()
返回单位的幂值 
unit.Unit()
将单元名称返回为字符串 
unit.DisplayUnit()
计算单位名称和单位幂值 (e.g. 'mm', 'cm3', etc) 
unit.Value()
返回当前计量单位中的单位的值 
unit.BaseValue()
返回该单位所在单位类别中的基本单位（某个单位类别会选定一个单位作为基本单位，所有其他单位在内部都以这些基本单位表示）。 
unit.Convert(string)
将单位值转换为同一类别的另一度量单位对应值。

全局对象方法直接可用，不需要调用任何对象申明。因此，指定“ PI”只执行全局对象的“ PI”方法。
global.bool()
返回“ Bool”类的类型定义。类型定义是不具有与其相关联的数据的类的一个特殊实例。只有静态方法（如 TypeName）能
在类型定义对象上调用。 
global.int()
返回“ int”类的类型定义。 
global.double()
返回“双精度”类的类型定义。 
global.string()
返回“文本”类的类型定义。 
global.pi()
返回 PI的双精度值 
global.e()
返回自然对数 e的双精度值 
global.min(numeric, ...)
返回参数的最小值： 
min(2,3,4,1,0,13,5) => 0 
global.max(numeric, ...)
返回参数的最大值： 
max(2,3,4,1,0,13,5) => 13 global.sqrt(double)
返回数字的平方根 
global.sin(double)
返回正弦值 
global.cos(double)
返回余弦值 
global.tan(double)
返回正切值 
global.atan(double)
返回余切值 
global.exp(double)
返回数的指数 (e^x) 
global.log(double)
返回值的自然对数 
global.iif(bool, expression, expression)
内部 IF函数。第一个参数是布尔表达式。如果表达式求值为真，则 IIF返回秒参数的值，否则返回第三个参数的值： 
iif('This is apple'.contains('apple'), 'apple', 'orange'an) => 'apple’
global.array([object, ...])
创建指定参数的对象数组： 
array('a','b','c').convert($item.repeat($index)).join(',') => 'a,bb,ccc’ 
global.chr(int)
通过指定的 Unicode字符代码返回字符： chr(65) => 'A’ 
global.rnd([int])
返回一个随机整数，在 0和参数中指定的值之间。如果参数被省略，则假定为 2147483647（0x7FFFFFF）。 rnd(100) => 返回 0 -100之间的随机数 . 
global.radix(int, int, [int])
将一个整数转换为给定进制，并以字符串形式返回。第一个参数指定要转换的整数值，第二个参数是目标进制。第三（可选）参数指定返回字符串的长度。字符串用“ 0”加前缀： radix(1023,8,6) => '001777' 
global.rgb(int, int, int)
返回具有指定三通道值的 RGB颜色： @fill.color = rgb(255,0,0) 
global.cmyk(int, int, int, int)
返回具有指定四色通道值的 CMYK颜色： @colors.find(cmyk(0,0,0,100)) 
global.cmy(int, int, int)
返回具有指定三色通道值的 CMY颜色： @colors.find(cmy(0,0,0)) 
global.hsb(int, int, int)
返回具有指定三色通道值的 HSB颜色： @fill.color.hsb = hsb(90,100,100) 
global.hls(int, int, int)
返回具有指定三色通道值的 HLS颜色： @colors.find(hls(0,0,0)) 
global.lab(double, int, int)
返回具有指定三色通道值的 Lab颜色： @outline.color.lab = lab(100,127,127) 
global.yiq(int, int, int)
返回具有指定三色通道值的 YIQ颜色： @colors.find(yiq(0,0,0)) 
global.vba()
返回允许执行 VBA功能的 VBA对象。请参见下面的 CQL部分调用 VBA宏以获得更多细节这是表示 CorelDRAW或Corel Designer文档中的 shape对象。当使用以下对象模型方法之一时， shape对象是可用的： 

Shapes.FindShapes  Shapes.FindShape  Shape.Evaluate
当执行这些方法时，可以通过调用前置“@”符号的方法来访问正在检查的当前 shape。例如，为了选择大于 2英寸的所有矩形，执行下面的 VBA命令： ActivePage.Shapes.FindShapes( _
              Query := "@type = 'rectangle' and @width > {2 in}").CreateSelection 
shape对象除了继承基本类的方法和属性外，还包含以下附加方法和属性：
shape.Name()
返回当前 shape的名称 @name='MyShape’ 
shape.Type()
返回一个表示 shape类型的字符串（返回的值将是矩形' rectangle'、椭圆' ellipse'、曲线 'curve'等）。 @type = 'polygon’ 
shape.Fill()
返回表示 shape填充的填充对象（见下文） @fill.type = 'uniform’ 
shape.Outline()
返回表示 shape轮廓的轮廓对象（见下文） @outline.color = 'red’ 
shape.Width()
返回当前计量单位中 shape的宽度 @width > {1 in} 
shape.Height()
返回当前计量单位中 shape的高度 @height = {20 mm} 
shape.Left()
返回对象左边缘在页面上的水平位置 
@left = {0 mm} 
shape.Right()
返回对象右边缘在页面上的水平位置 
@right > {8.5 in} 

填充对象表示 Shape的填充属性。 
fill.Type() 
shape.Top()
返回页面上对象顶部边缘的垂直位置。 
@top = {11 in} 
shape.Bottom()
返回页面上对象底部边缘的垂直位置。 
@bottom < {4 in} 
shape.CenterX()
返回页面上对象中心的水平位置 
@centerX = {4.25 in} 
shape.CenterY()
返回页面上对象中心的垂直位置 
@CenterY = {5.5 in} 
shape.Colors()
返回 shape的轮廓和填充中使用的颜色。此数组返回颜色值不重复，因此如果填充和轮廓都具有相同的颜色，则只返回一种颜色。 @colors.find(cmyk(0,0,0,100)) => 找出有 CMYK (0,0,0,100)色值的 shape @color.filter($item.cmyk.k <> 0).empty => 找出颜色分量 K不为 0的所有 shape 
shape.COM()
返回 shape的COM对象。这允许访问 CQL不能直接访问的 VBA对象模型的 Shape对象的方法和属性： (@com.transparency.type = 1) =>检查 Shape是否具有均匀的透明度。请参
阅CordelDRAW VBA对象模型，查看 VBA对象和方法的更多细节
返回的类型为字符串。可能的值是:无填充‘ none', 均匀填充 'uniform', 渐变填充 'fountain', 纹理填充 'postscript', 图样填充 'pattern',底纹填充 'texture', 网状填充 'hatch' @fill.type = 'fountain' and @fill.fountain.angle = 45 => 选择 45°渐变填充对象 
fill.Color()
返回均匀填充的颜色，如果填充不均匀，则为 NULL。 @fill.color = 'blue' => 选择蓝色均匀填充对象 
fill.Fountain()
返回渐变填充的属性，如果填充不是渐变返回 NULL。轮廓对象表示 shape的轮廓属性。 

outline.Type()返回表示轮廓的字符串类型。可能的值是:无轮廓' none'、实线轮廓“ solid”、虚线轮廓“ dot-dash”、增强' enhanced' @outline[.type = 'solid' or .type = 'dot-dash'] => 选择所有虚线或实线 shape 
outline.Color()
返回轮廓的颜色或 null如果 Shape没有轮廓 
outline.Width()
以点值返回轮廓的宽度: 
@outline > {1 pt} 
outline.IsHairline()
返回 true,如果是发丝轮廓 
outline.ScaleWithObject()
如果设置轮廓随对象缩放，则返回 true 
outline.BehindFill()
如果设置填充选项返回 true 
outline.LineCaps()返回的字符串表示轮廓线条端头类型。可能的值是:对接端头‘ butt’、圆形端头' round', 方形端头'square' 
outline.LineJoin()返回轮廓角类型,值为字符串。可能的值是：直角' miter', 圆角 'round', 斜角 'bevel' 

代表 CorelDRAW中的 color对象 
color.Type()
返回代表颜色模型的字符串。可能是‘ rgb’,“cmyk”、“ hsb”等等 
color.Name()返回颜色的名称,或如果颜色没有一个名字则返回 'unnamed color' 
color.Palette()
返回调色板颜色的名称,或若为自定义调色板的颜色则返回空字符串 
color.SourcePalette()
返回原始调色板用户设置颜色名称。 
color.PaletteIndex()
返回调色板中的颜色索引号。该方法只能返回固定调色板中的颜色 
color.Tint()
返回一个专色的色调值 
color.IsSpot()
如果颜色是专色返回 true 
color.IsCMYK()
返回 true,如果颜色是 CMYK模式 
color.IsGray()
返回 true,如果颜色是灰度模式 
color.IsWhite()
如果颜色是纯白色，返回 true 
color.IsTintable()
返回 true,如果颜色可以调 
color.COM()
返回 VBA的COM对象的颜色。这允许访问额外的未直接在 CQL中提供的颜色对象的方法和属性。 
color.RGB()
将颜色转换为 RGB和返回一个特殊版本的 CQL颜色对象，此颜色对象有三个属性- R,G,B，表示单独的颜色通道。 
@fill.color.rgb.r = 255 
color.CMYK()
将颜色转换为 CMYK和返回一个特殊版本的 CQL颜色对象，此颜色对象有四个属性- C,M,Y，K,表示单独的颜色通道。 
@fill.color.cmyk[.c = .m] 
color.CMY()
将颜色转换为 CMY和返回一个特殊版本的 CQL颜色对象，此颜色对象有三个属性-C,M,Y，表示单独的颜色通道。 
@fill.color.cmy.c = @outline.color.cmy.c 
color.Gray()
将颜色转换为灰度和灰度值返回一个整数: 
@fill.gray < 128 
color.HSB()
将颜色转换为 HSB和返回一个特殊版本的 CQL颜色对象，此颜色对象有三个属性-H,S,B，表示单独的颜色通道。 
@fill.color.hsb.h = 270 
color.HLS()
将颜色转换为 HLS和返回一个特殊版本的 CQL颜色对象，此颜色对象有三个属性-H,L,S，表示单独的颜色通道。 
@fill.color.hls.s = 0 
color.Lab()
将颜色转换为 Lab和返回一个特殊版本的 CQL颜色对象，此颜色对象有三个属性-L,a,b，表示单独的颜色通道。 
@fill.color.lab.l = 0 and @fill.color.lab.a = -10 
color.YIQ()
将颜色转换为 YIQ和返回一个特殊版本的 CQL颜色对象，此颜色对象有三个属性-Y,I,Q，表示单独的颜色通道。 
@fill.color.yiq.y < 10 

可以从 CQL调用 VBA宏函数。为此,通过使用下面的语法:使用 global对象的 vba属性提供访问公共 vba宏函数 
vba.Project.Module.Function(parameters)
例如 , 你可以添加以下 VBA 函数到 GlobalMacros > Module1: Public Function Sum(ByVal x As Double, ByVal y As Double) As Double    Sum = x + y End Function
然后通过查询语句调用它: 
MsgBox Application.Evaluate("vba.GlobalMacros.Module1.Sum(2, 3)")此计算结果= 5只有在模块中被设公共函数方可调用。私有函数、类模块或窗体中的函数不能通过 CQL查询语句调用。


