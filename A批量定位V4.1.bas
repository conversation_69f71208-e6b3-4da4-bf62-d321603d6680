Private Sub bc_Click()
    tl = False
    tc = False
    tr = False
    cl = False
    c = False
    cr = False
    bl = False
    bc = True
    br = False
    SaveButtonStates
    Pform.TextBox1.Value = 0
End Sub

Private Sub bl_Click()
    tl = False
    tc = False
    tr = False
    cl = False
    c = False
    cr = False
    bl = True
    bc = False
    br = False
    SaveButtonStates
End Sub

Private Sub br_Click()
    tl = False
    tc = False
    tr = False
    cl = False
    c = False
    cr = False
    bl = False
    bc = False
    br = True
    SaveButtonStates
End Sub

Private Sub c_Click()
    tl = False
    tc = False
    tr = False
    cl = True
    c = True
    cr = False
    bl = False
    bc = False
    br = False
    Pform.TextBox1.Value = 0
    Pform.TextBox2.Value = 0
    SaveButtonStates
End Sub


Private Sub cl_Click()
    tl = False
    tc = False
    tr = False
    cl = True
    c = False
    cr = False
    bl = False
    bc = False
    br = False
    Pform.TextBox2.Value = 0
    SaveButtonStates
End Sub

Private Sub CommandButton3_Click()
    AlignContainedShapes
End Sub

Private Sub cr_Click()
    tl = False
    tc = False
    tr = False
    cl = False
    c = False
    cr = True
    bl = False
    bc = False
    br = False
    Pform.TextBox2.Value = 0
    SaveButtonStates
End Sub


Private Sub inpc_Click()
    SaveSetting "JinGuangHe", "Pform", "inpc", IIf(Pform.inpc.Value, True, False)
     If Pform.inpc.Value = False Then
        Pform.zhai.Visible = False
        Pform.yuan.Visible = False
        Pform.chang.Visible = False
        Pform.man.Visible = False
        Pform.inpc.Caption = "置入图框"
        Pform.inpc.Width = 55
        Pform.tietu.Visible = True
     Else
        Pform.zhai.Visible = True
        Pform.yuan.Visible = True
        Pform.chang.Visible = True
        Pform.man.Visible = True
        Pform.inpc.Caption = ""
        Pform.inpc.Width = 13
        Pform.tietu.Visible = False
        Pform.tietu.Value = False
     End If
End Sub

Private Sub Label1_Click()
    Pform.TextBox1.Value = 0
End Sub

Private Sub Label2_Click()
    Pform.TextBox2.Value = 0
End Sub

Private Sub Label4_Click()
    Pform.TextBox1.Value = 0
    Pform.TextBox2.Value = 0
End Sub

Private Sub Label5_Click()
    Pform.TextBox1.Value = 3
    Pform.TextBox2.Value = 3
End Sub

Private Sub tc_Click()
    tl = False
    tc = True
    tr = False
    cl = False
    c = False
    cr = False
    bl = False
    bc = False
    br = False
    SaveButtonStates
    Pform.TextBox1.Value = 0
End Sub
Private Sub tietu_Click()
    If Pform.tietu.Value = True Then
        Pform.inpc.Value = False
    End If
End Sub

Private Sub tl_Click()
    tl = True
    tc = False
    tr = False
    cl = False
    c = False
    cr = False
    bl = False
    bc = False
    br = False
    SaveButtonStates
End Sub

Private Sub tr_Click()
    tl = False
    tc = False
    tr = True
    cl = False
    c = False
    cr = False
    bl = False
    bc = False
    br = False
    SaveButtonStates
End Sub
Private Sub SaveButtonStates()
    SaveSetting "JinGuangHe", "PformButtonStates", "tl", tl
    SaveSetting "JinGuangHe", "PformButtonStates", "tc", tc
    SaveSetting "JinGuangHe", "PformButtonStates", "tr", tr
    SaveSetting "JinGuangHe", "PformButtonStates", "cl", cl
    SaveSetting "JinGuangHe", "PformButtonStates", "c", c
    SaveSetting "JinGuangHe", "PformButtonStates", "cr", cr
    SaveSetting "JinGuangHe", "PformButtonStates", "bl", bl
    SaveSetting "JinGuangHe", "PformButtonStates", "bc", bc
    SaveSetting "JinGuangHe", "PformButtonStates", "br", br
End Sub

Private Sub LoadButtonStates()
    tl = CBool(GetSetting("JinGuangHe", "PformButtonStates", "tl", "False"))
    tc = CBool(GetSetting("JinGuangHe", "PformButtonStates", "tc", "False"))
    tr = CBool(GetSetting("JinGuangHe", "PformButtonStates", "tr", "False"))
    cl = CBool(GetSetting("JinGuangHe", "PformButtonStates", "cl", "False"))
    c = CBool(GetSetting("JinGuangHe", "PformButtonStates", "c", "true"))
    cr = CBool(GetSetting("JinGuangHe", "PformButtonStates", "cr", "False"))
    bl = CBool(GetSetting("JinGuangHe", "PformButtonStates", "bl", "False"))
    bc = CBool(GetSetting("JinGuangHe", "PformButtonStates", "bc", "False"))
    br = CBool(GetSetting("JinGuangHe", "PformButtonStates", "br", "False"))
End Sub

Private Sub TextBox1_Change()
    Dim mx As Double
    mx = Val(Pform.TextBox1.Value)
    SaveSetting "JinGuangHe", "Pform", "Pformmx", mx
End Sub

Private Sub TextBox2_Change()
    Dim my As Double
    my = Val(Pform.TextBox2.Value)
    SaveSetting "JinGuangHe", "Pform", "Pformmy", my
End Sub


Private Sub A刷新界面()
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
End Sub

Private Sub CommandButton2_MouseUp(ByVal Button As Integer, ByVal Shift As Integer, ByVal X As Single, ByVal Y As Single)
    If Button = 1 Then
        一键多对多替换 (False)
    ElseIf Button = 2 Then
        一键多对多替换 (True)
    ElseIf Button = 4 Then
        ' 中键点击，切换角度匹配状态
        ToggleAngleMatch
    End If
End Sub

' 切换角度匹配状态的函数
Private Sub ToggleAngleMatch()
    Dim currentAngleMatch As Boolean
    currentAngleMatch = GetSetting("JinGuangHe", "Pform", "AngleMatch", True)

    ' 切换状态
    currentAngleMatch = Not currentAngleMatch

    ' 保存新状态
    SaveSetting "JinGuangHe", "Pform", "AngleMatch", currentAngleMatch

    ' 更新按钮颜色
    If currentAngleMatch Then
        ' 匹配角度值 - 默认颜色
        Pform.CommandButton2.ForeColor = &H80&
    Else
        ' 不匹配角度值 - 蓝色
        Pform.CommandButton2.ForeColor = &HFF0000
    End If
End Sub

Private Sub UserForm_Initialize()

     LoadButtonStates
     
     Pform.inpc.Value = GetSetting("JinGuangHe", "Pform", "inpc", True)
     If Pform.inpc.Value = False Then
        Pform.zhai.Visible = False
        Pform.yuan.Visible = False
        Pform.chang.Visible = False
        Pform.man.Visible = False
        Pform.inpc.Caption = "置入图框"
        Pform.inpc.Width = 55
        Pform.tietu.Visible = True
     Else
        Pform.zhai.Visible = True
        Pform.yuan.Visible = True
        Pform.chang.Visible = True
        Pform.man.Visible = True
        Pform.inpc.Caption = ""
        Pform.inpc.Width = 13
        Pform.tietu.Visible = False
        Pform.tietu.Value = False
     End If
     
     '图框适配方式
        Dim shihe As String
        shihe = GetSetting("JinGuangHe", "pform", "shihe", "zhai") ' 默认选中 heise
    
        ' 根据存储的值设置单选控件的状态
        If shihe = "yuan" Then
            yuan.Value = True
            zhai.Value = False
            man.Value = False
            chang.Value = False
        ElseIf shihe = "zhai" Then
            yuan.Value = False
            man.Value = False
            zhai.Value = True
            chang.Value = False
        ElseIf shihe = "chang" Then
            yuan.Value = False
            zhai.Value = False
            chang.Value = True
            man.Value = False
        Else
            yuan.Value = False
            zhai.Value = False
            chang.Value = False
            man.Value = True
        End If
        
     tietu.Value = False

     savedmx = GetSetting("JinGuangHe", "pform", "pformmx", 0)
     Pform.TextBox1.Value = savedmx
     If savedmx = "" Then
        savedmx = 0
        Pform.TextBox1.Value = 0
     End If

     savedmy = GetSetting("JinGuangHe", "pform", "pformmy", 0)
     Pform.TextBox2.Value = savedmy
     If savedmy = "" Then
        savedmy = 0
        Pform.TextBox2.Value = 0
     End If


     With Me
    .StartUpPosition = 0
    .Left = Val(GetSetting("JinGuangHe", "pform", "windows_left", 900))
    .Top = Val(GetSetting("JinGuangHe", "pform", "windows_top", 200))
    End With

    ' 初始化角度匹配状态
    Dim angleMatch As Boolean
    angleMatch = GetSetting("JinGuangHe", "Pform", "AngleMatch", True)
    If angleMatch Then
        ' 匹配角度值 - 默认颜色
        Pform.CommandButton2.ForeColor = &H80&
    Else
        ' 不匹配角度值 - 蓝色
        Pform.CommandButton2.ForeColor = &HFF0000
    End If

    TextBox1.SetFocus
End Sub
Private Sub chang_Click()
    SaveSetting "JinGuangHe", "Pform", "shihe", "chang"
End Sub

Private Sub yuan_Click()
    SaveSetting "JinGuangHe", "Pform", "shihe", "yuan"
End Sub
Private Sub man_Click()
    SaveSetting "JinGuangHe", "Pform", "shihe", "man"
End Sub

Private Sub zhai_Click()
   SaveSetting "JinGuangHe", "Pform", "shihe", "zhai"
End Sub

Private Sub UserForm_QueryClose(Cancel As Integer, CloseMode As Integer)
    saveFormPos True
End Sub

Sub saveFormPos(bDoSave As Boolean)
  If bDoSave Then
    SaveSetting "JinGuangHe", "pform", "windows_left", Me.Left
    SaveSetting "JinGuangHe", "pform", "windows_top", Me.Top
  End If
End Sub

Sub AlignContainedShapes()
    Dim doc As Document
    Dim shapeDict As Object
    Dim shapeA As Shape, shapeB As Shape
    Dim shapeKey As Variant

    ' 获取当前文档
    Set doc = ActiveDocument
    ' 创建一个字典来存储形状的包含关系
    Set shapeDict = CreateObject("Scripting.Dictionary")

    ' 获取当前图层的所有形状
    Dim shapes As ShapeRange
    Set shapes = doc.ActiveLayer.shapes.All

    ' 遍历所有形状，判断包含关系
    For Each shapeA In shapes
        ' 初始化字典项
        If Not shapeDict.Exists(shapeA.StaticID) Then
            Dim innerDict As Object
            Set innerDict = CreateObject("Scripting.Dictionary")
            innerDict("Contains") = False ' 是否有被包含的对象
            Set innerDict("ContainedShapes") = CreateObject("Scripting.Dictionary") ' 存储被包含的对象
            shapeDict.Add shapeA.StaticID, innerDict
        End If

        ' 遍历所有其他形状，判断是否在 shapeA 内部
        For Each shapeB In shapes
            ' 跳过自身
            If shapeA.StaticID <> shapeB.StaticID Then
                ' 检查 shapeB 是否在 shapeA 内部
                If IsShapeInside(shapeB, shapeA) Then
                    ' 更新字典，标记包含关系
                    shapeDict(shapeA.StaticID)("Contains") = True
                    shapeDict(shapeA.StaticID)("ContainedShapes").Add shapeB.StaticID, shapeB
                End If
            End If
        Next shapeB
    Next shapeA
    
    ' 查找所有外框并将内部对象与外框居中对齐
    For Each shapeKey In shapeDict.Keys
        If shapeDict(shapeKey)("Contains") = True Then
            Dim containedShapes As New ShapeRange
            containedShapes.RemoveAll
            ' 收集被包含的形状
            Dim containedKey As Variant
            For Each containedKey In shapeDict(shapeKey)("ContainedShapes").Keys
                containedShapes.Add shapeDict(shapeKey)("ContainedShapes")(containedKey)
            Next containedKey

            ' 获取外框的形状
            Set shapeA = doc.ActiveLayer.shapes.FindShape(, , shapeKey)
            If Not shapeA Is Nothing And containedShapes.Count > 0 Then
                ' 计算外框的中心位置
                Dim centerX As Double, centerY As Double
                centerX = shapeA.LeftX + (shapeA.RightX - shapeA.LeftX) / 2
                centerY = shapeA.TopY + (shapeA.BottomY - shapeA.TopY) / 2

                ' 计算内部对象的中心位置
                Dim offsetX As Double, offsetY As Double
                offsetX = centerX - (containedShapes.LeftX + (containedShapes.RightX - containedShapes.LeftX) / 2)
                offsetY = centerY - (containedShapes.TopY + (containedShapes.BottomY - containedShapes.TopY) / 2)

                ' 移动内部对象到外框的中心
                containedShapes.Move offsetX, offsetY
            End If
        End If
    Next shapeKey
End Sub

Function IsShapeInside(shapeA As Shape, shapeB As Shape) As Boolean
    ' 获取形状 A 和 B 的边界值
    Dim A_Left As Double, A_Top As Double, A_Right As Double, A_Bottom As Double
    Dim B_Left As Double, B_Top As Double, B_Right As Double, B_Bottom As Double
    
    A_Left = shapeA.LeftX
    A_Top = shapeA.TopY
    A_Right = shapeA.RightX
    A_Bottom = shapeA.BottomY
    
    B_Left = shapeB.LeftX
    B_Top = shapeB.TopY
    B_Right = shapeB.RightX
    B_Bottom = shapeB.BottomY
    
    ' 判断形状 B 是否完全在形状 A 内部
    IsShapeInside = (B_Left >= A_Left And _
                     B_Top <= A_Top And _
                     B_Right <= A_Right And _
                     B_Bottom >= A_Bottom)
End Function

Private Sub 一键多对多替换(ByVal Rb As Boolean)
    Dim bEsc%, s As ShapeRange, sh As Shape, sr As ShapeRange, i!
    Dim x1 As Double, y1 As Double, x2 As Double, y2 As Double, Shift&
    Dim w As Double, h As Double, sw As Double, ssh As Double, sss As Double
    If ActiveDocument Is Nothing Then Exit Sub
    ActiveDocument.Unit = cdrMillimeter
    ActiveDocument.ReferencePoint = cdrCenter
    Application.Optimization = False
    ActiveDocument.BeginCommandGroup "一键多对多替换"
    
    
    Dim inpc As Boolean, zhai As Boolean, chang As Boolean, yuan As Boolean, man As Boolean, tietu As Boolean
    Dim angleMatch As Boolean
    inpc = Pform.inpc.Value
    zhai = Pform.zhai.Value
    yuan = Pform.yuan.Value
    chang = Pform.chang.Value
    man = Pform.man.Value
    tietu = Pform.tietu.Value
    angleMatch = GetSetting("JinGuangHe", "Pform", "AngleMatch", True)
    
    On Error Resume Next
    
    Dim mx As Double
    Dim my As Double
    
    mx = Pform.TextBox1.Value
    my = Pform.TextBox2.Value
    
    Set s = ActiveSelectionRange
    bEsc = ActiveDocument.GetUserArea(x1, y1, x2, y2, Shift, 10, False, cdrCursorWinCross)
    If Not bEsc Then
        Set sh = ActiveDocument.ActivePage.SelectShapesFromRectangle(x1, y1, x2, y2, False)
        Set sr = sh.shapes.All
        If sh.shapes.Count = 0 Then Exit Sub
    End If
    
    s.Sort " @shape1.Top * 100 - @shape1.Left > @shape2.Top * 100 - @shape2.Left"
    sr.Sort " @shape1.Top * 100 - @shape1.Left > @shape2.Top * 100 - @shape2.Left"
    Application.Optimization = True
    
    Dim originalSelection As New ShapeRange
    For i = 1 To s.shapes.Count
        originalSelection.Add s.shapes(i)
    Next i
    
    If Rb = True And sr.shapes.Count > s.shapes.Count Then
        Dim finalShapeRange As New ShapeRange
        Dim targetCount As Integer
        targetCount = sr.shapes.Count
        Dim sourceIndex As Integer
        sourceIndex = 1
        Dim origCount As Integer
        origCount = originalSelection.Count
        For i = 1 To targetCount
            Dim targetShape As Shape
            If i <= origCount Then
                Set targetShape = originalSelection.shapes(i)
            Else
                Dim copySourceIndex As Integer
                copySourceIndex = ((i - 1) Mod origCount) + 1
                Set targetShape = originalSelection.shapes(copySourceIndex).Duplicate
            End If

            finalShapeRange.Add targetShape
        Next i
        s.RemoveAll
        For i = 1 To finalShapeRange.Count
            s.Add finalShapeRange.shapes(i)
        Next i
    End If
    
    If s.shapes.Count < sr.shapes.Count Then
        Dim ti As Integer
        ti = s.shapes.Count
    Else
        ti = sr.shapes.Count
    End If
    
    For i = 1 To ti
        If s.shapes(i).StaticID <> sr.shapes(i).StaticID Then
        
            If tietu = True Then
                Set effEnvelope = s.shapes(i).CreateEnvelopeFromShape(sr.shapes(i), cdrEnvelopePutty, False)
                s.shapes(i).SetPositionEx cdrReferencePoint.cdrCenter, sr.shapes(i).centerX, sr.shapes(i).centerY
                s.shapes(i).OrderToFront
            Else
        
                If tl = True Then '顶部左对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).TopY = sr.shapes(i).TopY - my
                    s.shapes(i).LeftX = sr.shapes(i).LeftX + mx
                    
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf tc = True Then '顶部居中对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).TopY = sr.shapes(i).TopY - my
                    s.shapes(i).centerX = sr.shapes(i).centerX + mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf tr = True Then '顶部右对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).TopY = sr.shapes(i).TopY - my
                    s.shapes(i).RightX = sr.shapes(i).RightX - mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf cl = True Then '垂直居中，左对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).centerY = sr.shapes(i).centerY + my
                    s.shapes(i).LeftX = sr.shapes(i).LeftX + mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf c = True Then '中心对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).centerY = sr.shapes(i).centerY + my
                    s.shapes(i).centerX = sr.shapes(i).centerX + mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf cr = True Then  '垂直居中，右对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).centerY = sr.shapes(i).centerY + my
                    s.shapes(i).RightX = sr.shapes(i).RightX - mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf bl = True Then '底部左对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).BottomY = sr.shapes(i).BottomY + my
                    s.shapes(i).LeftX = sr.shapes(i).LeftX + mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf bc = True Then '底部居中对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).BottomY = sr.shapes(i).BottomY + my
                    s.shapes(i).centerX = sr.shapes(i).centerX + mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                ElseIf br = True Then '底部右对齐
                    If angleMatch Then s.shapes(i).RotationAngle = sr.shapes(i).RotationAngle
                    s.shapes(i).BottomY = sr.shapes(i).BottomY + my
                    s.shapes(i).RightX = sr.shapes(i).RightX - mx
                    If inpc Then
                        w = sr.shapes(i).SizeWidth
                        h = sr.shapes(i).SizeHeight
                        sw = s.shapes(i).SizeWidth
                        ssh = s.shapes(i).SizeHeight
                        wb = w / sw
                        hb = h / ssh
                        If zhai Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            Else
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            End If
                        ElseIf chang Then
                            If w / h > sw / ssh Then
                                s.shapes(i).SizeWidth = sw * (h / ssh)
                                s.shapes(i).SizeHeight = h
                            Else
                                s.shapes(i).SizeHeight = ssh * (w / sw)
                                s.shapes(i).SizeWidth = w
                            End If
                        ElseIf yuan Then
                            s.shapes(i).SizeWidth = sw
                            s.shapes(i).SizeHeight = ssh
                        ElseIf man Then
                            s.shapes(i).SizeWidth = w
                            s.shapes(i).SizeHeight = h
                        End If
                        s.shapes(i).AddToPowerClip sr.shapes(i)
                    End If
                    
                End If
            
            End If
        End If
    Next i
        
    ActiveDocument.ClearSelection
    ActiveDocument.EndCommandGroup
    Application.Optimization = False
    ActiveWindow.Refresh: Application.Refresh
    Exit Sub
ErrorHandler:
    Application.Optimization = False
End Sub